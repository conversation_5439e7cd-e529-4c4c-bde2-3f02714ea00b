/**
 * API Service for communicating with the backend
 * Enhanced with standardized response handling
 */

import axios from 'axios';
import {
  normalizeApiResponse,
  handleApiError
} from '../utils/apiResponseHandler';
import { setupSimpleProfileFallback } from '../utils/apiInterceptor';

class ApiService {
  constructor() {
    this.baseURL = null;
    this.client = null;
    this.initialized = false;
    this.initPromise = this.init();
    this.pendingToken = null;
  }

  async init() {
    try {
      // Check if running in Electron or web browser
      if (window.electronAPI) {
        // Electron environment
        this.baseURL = await window.electronAPI.getBackendUrl();
      } else {
        // Web browser environment
        this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
      }

      // Create axios instance
      this.client = axios.create({
        baseURL: this.baseURL,
        timeout: 30000,
        withCredentials: true, // Include cookies in requests
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Request interceptor
      this.client.interceptors.request.use(
        (config) => {
          console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
          console.log(`🔍 [API] Request Headers:`, JSON.stringify(config.headers, null, 2));
          return config;
        },
        (error) => {
          console.error('API Request Error:', error);
          return Promise.reject(error);
        }
      );

      // Response interceptor
      this.client.interceptors.response.use(
        (response) => {
          console.log(`API Response: ${response.status} ${response.config.url}`);
          console.log('📊 Response data:', response.data);
          console.log('📊 Response headers:', response.headers);

          // Skip normalization for Instagram status API
          if (response.config.url && response.config.url.includes('/api/instagram-scraping/status/')) {
            console.log('🔄 [API] Bypassing normalization for Instagram status API');
            return response; // Return original response without normalization
          }

          // Normalize response to standard format
          const normalizedResponse = normalizeApiResponse(response.data, response.status);

          // Return normalized response while maintaining original structure for backward compatibility
          return {
            ...response,
            data: normalizedResponse,
            originalData: response.data // Keep original data for debugging
          };
        },
        (error) => {
          console.error('API Response Error:', error);
          console.error('📊 Error response data:', error.response?.data);
          console.error('📊 Error response status:', error.response?.status);

          // Use standardized error handling
          const standardError = handleApiError(error);

          // Create a new error with standardized format
          const enhancedError = new Error(standardError.message);
          enhancedError.response = {
            data: standardError,
            status: standardError.statusCode
          };

          throw enhancedError;
        }
      );

      // Set pending token if it exists
      if (this.pendingToken) {
        console.log('🔄 [API] Setting pending token after client initialization');
        this.setAuthToken(this.pendingToken);
        this.pendingToken = null;
      }

      // Setup profile sync interceptor
      setupSimpleProfileFallback(this.client);

      this.initialized = true;
      console.log('✅ [API] API service initialized successfully');
      console.log('🔗 [API] Base URL:', this.baseURL);
    } catch (error) {
      console.error('Failed to initialize API service:', error);
      throw error;
    }
  }

  // Set authentication token
  async setAuthToken(token) {
    // Wait for initialization if not ready
    if (!this.initialized) {
      console.warn('⚠️ [API] Client not initialized yet, storing token for later...');
      this.pendingToken = token;
      await this.initPromise;
    }

    // Double check client exists after initialization
    if (!this.client) {
      console.error('❌ [API] Client is null after initialization!');
      return;
    }

    if (token) {
      this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      console.log('✅ [API] Auth token set in API client:', token.substring(0, 20) + '...');
      console.log('🔍 [API] Current headers:', this.client.defaults.headers.common);
    } else {
      delete this.client.defaults.headers.common['Authorization'];
      console.log('🔄 [API] Auth token removed from API client');
    }
  }

  // Generic HTTP methods
  async get(url, config = {}) {
    console.log(`🔍 [API] GET ${url} - Headers:`, JSON.stringify(this.client.defaults.headers.common, null, 2));
    const response = await this.client.get(url, config);
    return response.data;
  }

  async post(url, data = {}, config = {}) {
    const response = await this.client.post(url, data, config);
    console.log(`📊 [API] POST ${url} - Full response:`, response);
    console.log(`📊 [API] POST ${url} - Response data:`, response.data);
    console.log(`📊 [API] POST ${url} - Response data JSON:`, JSON.stringify(response.data, null, 2));
    return response.data;
  }

  async put(url, data = {}, config = {}) {
    const response = await this.client.put(url, data, config);
    return response.data;
  }

  async delete(url, config = {}) {
    const response = await this.client.delete(url, config);
    return response.data;
  }

  // Profile Management APIs
  async getProfiles() {
    return this.get('/api/profiles/');
  }

  async createProfile(profileData) {
    return this.post('/api/profiles/', profileData);
  }

  async updateProfile(profileId, profileData) {
    return this.put(`/api/profiles/${profileId}`, profileData);
  }

  async deleteProfile(profileId) {
    return this.delete(`/api/profiles/${profileId}`);
  }

  async testProfile(profileId) {
    return this.post(`/api/profiles/${profileId}/test`);
  }

  async loginFacebook(profileId, credentials) {
    return this.post(`/api/profiles/${profileId}/login`, credentials);
  }

  async launchBrowser(profileId, headless = false) {
    return this.post(`/api/profiles/${profileId}/launch-browser?headless=${headless}`);
  }

  // Save browser data for a profile
  async saveBrowserData(profileId, data) {
    return this.post(`/api/profiles/${profileId}/save-browser-data`, data);
  }

  // Load browser data for a profile
  async loadBrowserData(profileId) {
    return this.get(`/api/profiles/${profileId}/load-browser-data`);
  }

  async openFacebook(profileId) {
    return this.post(`/api/profiles/${profileId}/open-facebook`);
  }

  async openSocial(profileId) {
    return this.post(`/api/profiles/${profileId}/open-social`);
  }

  async completeFacebookLogin(profileId) {
    console.log('🚀 [API] completeFacebookLogin called for profile:', profileId);

    // Use raw response to avoid normalization issues
    console.log('🔄 [API] Making POST request to /api/profiles/' + profileId + '/complete-login');
    const response = await this.post(`/api/profiles/${profileId}/complete-login`);
    console.log('✅ [API] POST request completed');
    console.log('🔍 [API] Raw response:', response);
    console.log('🔍 [API] Response type:', typeof response);
    console.log('🔍 [API] Response keys:', Object.keys(response || {}));

    // Return originalData if available (to bypass normalization)
    if (response && response.originalData) {
      console.log('🔍 [API] Using originalData to bypass normalization');
      console.log('🔍 [API] originalData:', response.originalData);
      console.log('🔍 [API] originalData type:', typeof response.originalData);
      console.log('🔍 [API] originalData keys:', Object.keys(response.originalData || {}));
      return response.originalData;
    }

    // Fallback to normalized data
    if (response && response.data) {
      console.log('🔍 [API] Using normalized data');
      console.log('🔍 [API] data:', response.data);
      console.log('🔍 [API] data type:', typeof response.data);
      console.log('🔍 [API] data keys:', Object.keys(response.data || {}));
      return response.data;
    }

    // Fallback to direct response
    console.log('🔍 [API] Using direct response');
    console.log('🔍 [API] response:', response);
    return response;
  }

  async getBrowserStatus(profileId) {
    return this.get(`/api/profiles/${profileId}/browser-status`);
  }

  async closeBrowser(profileId) {
    return this.post(`/api/profiles/${profileId}/close-browser`);
  }

  // Scraping APIs
  async startScraping(scrapingConfig) {
    return this.post('/api/scraping/start', scrapingConfig);
  }

  async getScrapingStatus(taskId) {
    return this.get(`/api/scraping/status/${taskId}`);
  }

  async stopScraping(taskId) {
    return this.post(`/api/scraping/stop/${taskId}`);
  }

  async getScrapingResults(taskId) {
    return this.get(`/api/scraping/results/${taskId}`);
  }

  // Instagram Scraping APIs
  async startInstagramScraping(scrapingConfig) {
    return this.post('/api/instagram-scraping/start', scrapingConfig);
  }

  async getInstagramScrapingStatus(taskId) {
    // Direct call without normalization for Instagram status
    const response = await this.client.get(`/api/instagram-scraping/status/${taskId}`);
    return response.data;
  }

  async stopInstagramScraping(taskId) {
    return this.post(`/api/instagram-scraping/stop/${taskId}`);
  }

  async getInstagramScrapingResults(taskId) {
    return this.get(`/api/instagram-scraping/results/${taskId}`);
  }

  async exportInstagramScrapingResults(taskId, format = 'excel') {
    return this.post(`/api/instagram-scraping/export/${taskId}`, { format });
  }

  async exportScrapingResults(taskId, format = 'excel') {
    return this.get(`/api/scraping/export/${taskId}?format=${format}`, {
      responseType: 'blob'
    });
  }

  // Messaging APIs
  async startMessaging(messagingConfig) {
    return this.post('/api/messaging/start', messagingConfig);
  }

  async getMessagingStatus(taskId) {
    return this.get(`/api/messaging/status/${taskId}`);
  }

  async stopMessaging(taskId) {
    return this.post(`/api/messaging/stop/${taskId}`);
  }

  async getMessagingResults(taskId) {
    return this.get(`/api/messaging/results/${taskId}`);
  }

  async uploadRecipientList(file) {
    const formData = new FormData();
    formData.append('file', file);

    return this.post('/api/messaging/upload-recipients', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  // Generic file upload method
  async uploadFile(url, formData) {
    console.log(`📤 [API] Uploading file to: ${url}`);
    console.log(`📤 [API] FormData entries:`, Array.from(formData.entries()));

    try {
      // For FormData, we need to remove the default Content-Type header
      // and let the browser set it automatically with boundary
      const response = await this.post(url, formData, {
        headers: {
          'Content-Type': undefined, // Remove default Content-Type
        },
      });

      console.log(`✅ [API] File upload successful:`, response);
      console.log(`✅ [API] Response JSON:`, JSON.stringify(response, null, 2));
      console.log(`✅ [API] Response type:`, typeof response);
      console.log(`✅ [API] Response.valid:`, response?.valid);

      return response;
    } catch (error) {
      console.error(`❌ [API] File upload failed:`, error);
      throw error;
    }
  }

  // Instagram Direct Message APIs
  async validateInstagramDMFile(file) {
    console.log(`📁 [API] Validating Instagram DM file: ${file.name}`);
    console.log(`📁 [API] File size: ${file.size} bytes`);
    console.log(`📁 [API] File type: ${file.type}`);

    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await this.uploadFile('/api/instagram/direct-message/validate-file', formData);
      console.log(`✅ [API] Instagram DM file validation successful:`, response);
      console.log(`✅ [API] Validation response JSON:`, JSON.stringify(response, null, 2));
      console.log(`✅ [API] Validation response.valid:`, response?.valid);
      console.log(`✅ [API] Validation response type:`, typeof response);

      return response;
    } catch (error) {
      console.error(`❌ [API] Instagram DM file validation failed:`, error);
      throw error;
    }
  }

  async startInstagramDirectMessage(config) {
    console.log(`🚀 [API] Starting Instagram Direct Message task:`, config);

    try {
      // If image files are included, we need to upload them first
      if (config.attach_images && config.image_files && config.image_files.length > 0) {
        console.log(`📁 [API] Uploading ${config.image_files.length} image files...`);

        // Create FormData for image files
        const formData = new FormData();
        config.image_files.forEach((file, index) => {
          formData.append(`image_${index}`, file);
        });

        // Upload images and get file paths
        const uploadResponse = await this.uploadFile('/api/instagram/direct-message/upload-images', formData);
        console.log(`✅ [API] Images uploaded:`, uploadResponse);

        // Update config with uploaded image paths
        config.image_file_paths = uploadResponse.file_paths;
        delete config.image_files; // Remove file objects from config
      }

      const response = await this.post('/api/instagram/direct-message/start', { config });
      console.log(`✅ [API] Instagram DM task started:`, response);
      return response;
    } catch (error) {
      console.error(`❌ [API] Failed to start Instagram DM task:`, error);
      throw error;
    }
  }

  async getInstagramDMStatus(taskId) {
    console.log(`🔍 [API] Getting Instagram DM status for task: ${taskId}`);

    try {
      const response = await this.get(`/api/instagram/direct-message/status/${taskId}`);
      console.log(`✅ [API] Instagram DM status retrieved:`, response);
      return response;
    } catch (error) {
      console.error(`❌ [API] Failed to get Instagram DM status:`, error);
      throw error;
    }
  }

  async stopInstagramDM(taskId) {
    console.log(`🛑 [API] Stopping Instagram DM task: ${taskId}`);

    try {
      const response = await this.post(`/api/instagram/direct-message/stop/${taskId}`);
      console.log(`✅ [API] Instagram DM task stopped:`, response);
      return response;
    } catch (error) {
      console.error(`❌ [API] Failed to stop Instagram DM task:`, error);
      throw error;
    }
  }

  async getInstagramDMTasks() {
    console.log(`📋 [API] Getting active Instagram DM tasks`);

    try {
      const response = await this.get('/api/instagram/direct-message/tasks');
      console.log(`✅ [API] Instagram DM tasks retrieved:`, response);
      return response;
    } catch (error) {
      console.error(`❌ [API] Failed to get Instagram DM tasks:`, error);
      throw error;
    }
  }

  async getCompletedInstagramDMTasks() {
    console.log(`📋 [API] Getting completed Instagram DM tasks`);

    try {
      const response = await this.get('/api/instagram/direct-message/completed-tasks');
      console.log(`✅ [API] Completed Instagram DM tasks retrieved:`, response);
      return response;
    } catch (error) {
      console.error(`❌ [API] Failed to get completed Instagram DM tasks:`, error);
      throw error;
    }
  }

  // Profile Groups APIs (NestJS endpoints)
  async getProfileGroups() {
    return this.get('http://localhost:3000/profiles/groups', {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  async createProfileGroup(groupData) {
    return this.post('http://localhost:3000/profiles/groups', groupData, {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  async updateProfileGroup(groupId, groupData) {
    return this.put(`http://localhost:3000/profiles/groups/${groupId}`, groupData, {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  async deleteProfileGroup(groupId) {
    return this.delete(`http://localhost:3000/profiles/groups/${groupId}`, {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  async getProfileItems() {
    return this.get('http://localhost:3000/profiles/items', {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  async createProfileItem(profileData) {
    return this.post('http://localhost:3000/profiles/items', profileData, {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  async updateProfileItem(profileId, profileData) {
    return this.put(`http://localhost:3000/profiles/items/${profileId}`, profileData, {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  async deleteProfileItem(profileId) {
    return this.delete(`http://localhost:3000/profiles/items/${profileId}`, {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  async createProfileItem(profileData) {
    return this.post('http://localhost:3000/profiles/items', profileData, {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  async getAccounts() {
    return this.get('http://localhost:3000/accounts', {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  async getUsers() {
    return this.get('http://localhost:3000/auth/users', {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  async getUserProfileGroupAccess() {
    return this.get('http://localhost:3000/profiles/group-access', {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  async createUserProfileGroupAccess(accessData) {
    return this.post('http://localhost:3000/profiles/group-access', accessData, {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  async updateUserProfileGroupAccess(accessId, accessData) {
    return this.put(`http://localhost:3000/profiles/group-access/${accessId}`, accessData, {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  async deleteUserProfileGroupAccess(accessId) {
    return this.delete(`http://localhost:3000/profiles/group-access/${accessId}`, {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  // Profile Data Management APIs
  async saveProfileData(accountId, profileData) {
    return this.post(`http://localhost:3000/profiles/${accountId}/save-profile`, profileData, {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  async getProfileData(accountId) {
    return this.get(`http://localhost:3000/profiles/${accountId}/profile-data`, {
      baseURL: '', // Override baseURL for NestJS calls
      withCredentials: true
    });
  }

  async launchBrowserWithProfile(accountId) {
    try {
      console.log(`🚀 Launching browser for account ${accountId} via NestJS`);
      const response = await this.client.post(`http://localhost:3000/profiles/${accountId}/launch-browser`, {}, {
        baseURL: '', // Override baseURL for NestJS calls
        withCredentials: true,
        timeout: 60000, // Increase timeout for browser launch
      });

      console.log('✅ NestJS launch response:', response);
      return response;
    } catch (error) {
      console.error('❌ NestJS launch error:', error);
      throw error;
    }
  }

  async launchBrowserWithProfileData(accountId) {
    try {
      console.log(`🚀 Launching browser with profile data for account ${accountId} via NestJS`);
      const response = await this.client.post(`http://localhost:3000/profiles/${accountId}/launch-browser-with-data`, {}, {
        baseURL: '', // Override baseURL for NestJS calls
        withCredentials: true,
        timeout: 60000, // Increase timeout for browser launch
      });

      console.log('✅ NestJS launch with data response:', response);
      return response;
    } catch (error) {
      console.error('❌ NestJS launch with data error:', error);
      throw error;
    }
  }

  // Login Status Check APIs
  async checkLoginStatus(profileIds, platforms = ['facebook', 'instagram']) {
    console.log('🌐 [API] checkLoginStatus called with:', { profileIds, platforms });

    const requestData = {
      profile_ids: profileIds,
      check_type: 'bulk',
      platforms: platforms
    };

    console.log('🌐 [API] checkLoginStatus request data:', requestData);

    try {
      const response = await this.post('/api/profiles/check-login-status', requestData);
      console.log('✅ [API] checkLoginStatus response:', response);
      return response;
    } catch (error) {
      console.error('❌ [API] checkLoginStatus error:', error);
      throw error;
    }
  }

  async checkSingleProfileLoginStatus(profileId, platform = 'facebook') {
    console.log('🌐 [API] checkSingleProfileLoginStatus called with:', { profileId, platform });

    const requestData = {
      profile_ids: [profileId],
      check_type: 'single',
      platforms: [platform]
    };

    console.log('🌐 [API] checkSingleProfileLoginStatus request data:', requestData);

    try {
      const response = await this.post('/api/profiles/check-login-status', requestData);
      console.log('✅ [API] checkSingleProfileLoginStatus response:', response);
      return response;
    } catch (error) {
      console.error('❌ [API] checkSingleProfileLoginStatus error:', error);
      throw error;
    }
  }

  // System APIs
  async getSystemStatus() {
    return this.get('/health');
  }

  async getSystemStats() {
    return this.get('/api/system/stats');
  }
}

// Create and export singleton instance
export const apiService = new ApiService();

// Also export as default for backward compatibility
export default apiService;
