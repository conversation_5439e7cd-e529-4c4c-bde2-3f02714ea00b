<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Login Status Checker</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .debug-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        .info { color: #1890ff; }
        .warning { color: #faad14; }
    </style>
</head>
<body>
    <h1>🔍 Login Status Checker Debug Tool</h1>
    
    <div class="debug-section">
        <h3>📊 System Status</h3>
        <p>Backend URL: <span id="backend-url">http://127.0.0.1:8000</span></p>
        <p>Frontend URL: <span id="frontend-url">http://localhost:3000</span></p>
        <button onclick="checkBackendHealth()">Check Backend Health</button>
        <button onclick="checkFrontendAPI()">Check Frontend API Service</button>
    </div>

    <div class="debug-section">
        <h3>🧪 API Testing</h3>
        <button onclick="testLoginStatusAPI()">Test Login Status API</button>
        <button onclick="testGetProfiles()">Test Get Profiles</button>
        <button onclick="testAuthentication()">Test Authentication</button>
    </div>

    <div class="debug-section">
        <h3>📋 Debug Logs</h3>
        <button onclick="clearLogs()">Clear Logs</button>
        <div id="debug-logs" class="log"></div>
    </div>

    <script>
        const BACKEND_URL = 'http://127.0.0.1:8000';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debug-logs');
            const logEntry = `[${timestamp}] ${message}\n`;
            
            const span = document.createElement('span');
            span.className = type;
            span.textContent = logEntry;
            
            logElement.appendChild(span);
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('debug-logs').innerHTML = '';
        }

        async function checkBackendHealth() {
            log('🔍 Checking backend health...', 'info');
            
            try {
                const response = await fetch(`${BACKEND_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Backend is healthy: ${JSON.stringify(data)}`, 'success');
                } else {
                    log(`⚠️ Backend responded with status: ${response.status}`, 'warning');
                }
            } catch (error) {
                log(`❌ Backend health check failed: ${error.message}`, 'error');
            }
        }

        async function checkFrontendAPI() {
            log('🔍 Checking frontend API service...', 'info');
            
            // Check if we can access the API service
            if (typeof window !== 'undefined') {
                log('✅ Running in browser environment', 'success');
                
                // Try to simulate API call structure
                const testRequest = {
                    profile_ids: [1, 2],
                    check_type: 'bulk',
                    platforms: ['facebook', 'instagram']
                };
                
                log(`📋 Test request structure: ${JSON.stringify(testRequest, null, 2)}`, 'info');
            } else {
                log('❌ Not running in browser environment', 'error');
            }
        }

        async function testGetProfiles() {
            log('🔍 Testing get profiles API...', 'info');
            
            try {
                const response = await fetch(`${BACKEND_URL}/api/profiles/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        // Note: In real app, you'd need proper authentication token
                    }
                });
                
                log(`📊 Get profiles response status: ${response.status}`, response.ok ? 'success' : 'warning');
                
                if (response.status === 401) {
                    log('🔐 Authentication required (expected)', 'info');
                } else if (response.ok) {
                    const data = await response.json();
                    log(`✅ Profiles data: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    log(`❌ Error response: ${errorText}`, 'error');
                }
            } catch (error) {
                log(`❌ Get profiles failed: ${error.message}`, 'error');
            }
        }

        async function testLoginStatusAPI() {
            log('🔍 Testing login status API...', 'info');
            
            const testRequest = {
                profile_ids: [1, 2],
                check_type: 'bulk',
                platforms: ['facebook', 'instagram']
            };
            
            log(`📋 Sending request: ${JSON.stringify(testRequest, null, 2)}`, 'info');
            
            try {
                const response = await fetch(`${BACKEND_URL}/api/profiles/check-login-status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        // Note: In real app, you'd need proper authentication token
                    },
                    body: JSON.stringify(testRequest)
                });
                
                log(`📊 Login status API response status: ${response.status}`, response.ok ? 'success' : 'warning');
                
                if (response.status === 401) {
                    log('🔐 Authentication required (expected)', 'info');
                    log('✅ API endpoint exists and is protected', 'success');
                } else if (response.ok) {
                    const data = await response.json();
                    log(`✅ Login status response: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    log(`❌ Error response: ${errorText}`, 'error');
                }
            } catch (error) {
                log(`❌ Login status API failed: ${error.message}`, 'error');
            }
        }

        async function testAuthentication() {
            log('🔍 Testing authentication endpoints...', 'info');
            
            try {
                // Test health endpoint (should not require auth)
                const healthResponse = await fetch(`${BACKEND_URL}/health`);
                log(`📊 Health endpoint status: ${healthResponse.status}`, healthResponse.ok ? 'success' : 'error');
                
                // Test protected endpoint (should require auth)
                const protectedResponse = await fetch(`${BACKEND_URL}/api/profiles/`);
                log(`📊 Protected endpoint status: ${protectedResponse.status}`, 'info');
                
                if (protectedResponse.status === 401) {
                    log('✅ Authentication is working (401 for protected endpoint)', 'success');
                } else {
                    log('⚠️ Unexpected response from protected endpoint', 'warning');
                }
                
            } catch (error) {
                log(`❌ Authentication test failed: ${error.message}`, 'error');
            }
        }

        // Auto-run basic checks on page load
        window.addEventListener('load', () => {
            log('🚀 Debug tool loaded', 'info');
            log('📝 Click buttons above to run tests', 'info');
            
            // Auto-check backend health
            setTimeout(checkBackendHealth, 1000);
        });
    </script>
</body>
</html>
