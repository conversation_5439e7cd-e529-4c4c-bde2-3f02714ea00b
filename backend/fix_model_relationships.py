#!/usr/bin/env python3
"""
Fix SQLAlchemy model relationships to avoid circular dependency issues
"""

import os
import sys
from pathlib import Path

def fix_profile_model():
    """Fix Profile model by removing problematic relationships"""
    profile_model_path = Path(__file__).parent / "app" / "models" / "profile.py"
    
    print(f"📝 Fixing Profile model: {profile_model_path}")
    
    try:
        with open(profile_model_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if relationship is already commented out
        if "# instagram_scraping_tasks = relationship" in content:
            print("✅ Profile model relationship already fixed")
            return True
        
        # Replace the problematic relationship
        old_relationship = 'instagram_scraping_tasks = relationship("InstagramScrapingTask", back_populates="profile", lazy="dynamic")'
        new_relationship = '# instagram_scraping_tasks = relationship("InstagramScrapingTask", back_populates="profile", lazy="dynamic")  # Commented out to avoid circular dependency'
        
        if old_relationship in content:
            content = content.replace(old_relationship, new_relationship)
            
            with open(profile_model_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ Profile model relationship fixed")
            return True
        else:
            print("⚠️ Profile model relationship not found or already fixed")
            return True
            
    except Exception as e:
        print(f"❌ Failed to fix Profile model: {e}")
        return False

def fix_instagram_scraping_model():
    """Fix InstagramScrapingTask model by removing back_populates"""
    instagram_model_path = Path(__file__).parent / "app" / "models" / "instagram_scraping.py"
    
    print(f"📝 Fixing InstagramScrapingTask model: {instagram_model_path}")
    
    try:
        with open(instagram_model_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if relationship is already commented out
        if "# profile = relationship" in content:
            print("✅ InstagramScrapingTask model relationship already fixed")
            return True
        
        # Replace the problematic relationship
        old_relationship = 'profile = relationship("Profile", back_populates="instagram_scraping_tasks")'
        new_relationship = '# profile = relationship("Profile", back_populates="instagram_scraping_tasks")  # Commented out to avoid circular dependency'
        
        if old_relationship in content:
            content = content.replace(old_relationship, new_relationship)
            
            with open(instagram_model_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ InstagramScrapingTask model relationship fixed")
            return True
        else:
            print("⚠️ InstagramScrapingTask model relationship not found or already fixed")
            return True
            
    except Exception as e:
        print(f"❌ Failed to fix InstagramScrapingTask model: {e}")
        return False

def fix_main_imports():
    """Fix main.py imports to temporarily disable instagram_scraping"""
    main_path = Path(__file__).parent / "main.py"
    
    print(f"📝 Fixing main.py imports: {main_path}")
    
    try:
        with open(main_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if imports are already fixed
        if "# Temporarily comment out instagram_scraping" in content:
            print("✅ main.py imports already fixed")
            return True
        
        # Fix the import line
        old_import = "from app.api.routes import profiles, scraping, messaging, system, auth, profile_sharing, profile_websocket, profile_security, instagram_scraping, instagram_direct_message"
        new_import = """from app.api.routes import profiles, scraping, messaging, system, auth, profile_sharing, profile_websocket, profile_security, instagram_direct_message
# Temporarily comment out instagram_scraping to avoid relationship issues
# from app.api.routes import instagram_scraping"""
        
        if old_import in content:
            content = content.replace(old_import, new_import)
        
        # Fix the router include line
        old_router = "app.include_router(instagram_scraping.router, prefix=\"/api/instagram-scraping\", tags=[\"instagram-scraping\"])"
        new_router = "# Temporarily comment out instagram_scraping router to avoid relationship issues\n# app.include_router(instagram_scraping.router, prefix=\"/api/instagram-scraping\", tags=[\"instagram-scraping\"])"
        
        if old_router in content:
            content = content.replace(old_router, new_router)
        
        with open(main_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ main.py imports fixed")
        return True
        
    except Exception as e:
        print(f"❌ Failed to fix main.py imports: {e}")
        return False

def test_imports():
    """Test if imports work without errors"""
    print("🧪 Testing imports...")
    
    try:
        # Test Profile model import
        sys.path.insert(0, str(Path(__file__).parent))
        from app.models.profile import Profile
        print("✅ Profile model imports successfully")
        
        # Test database connection
        from app.core.database import AsyncSessionLocal
        from sqlalchemy import select
        
        import asyncio
        
        async def test_db():
            async with AsyncSessionLocal() as db:
                result = await db.execute(select(Profile).limit(1))
                profiles = result.scalars().all()
                print(f"✅ Database query successful, found {len(profiles)} profiles")
                return True
        
        asyncio.run(test_db())
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def main():
    """Main function to fix all relationship issues"""
    print("🔧 Fixing SQLAlchemy Model Relationships")
    print("=" * 50)
    
    success = True
    
    # Fix Profile model
    if not fix_profile_model():
        success = False
    
    # Fix InstagramScrapingTask model
    if not fix_instagram_scraping_model():
        success = False
    
    # Fix main.py imports
    if not fix_main_imports():
        success = False
    
    # Test imports
    if success and not test_imports():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All relationship issues fixed successfully!")
        print("\n📝 What was fixed:")
        print("1. Commented out circular relationship in Profile model")
        print("2. Commented out back_populates in InstagramScrapingTask model")
        print("3. Temporarily disabled instagram_scraping router in main.py")
        print("4. Verified imports work correctly")
        print("\n🚀 You can now start the backend server without relationship errors")
        return 0
    else:
        print("❌ Some fixes failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
