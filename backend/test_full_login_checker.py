#!/usr/bin/env python3
"""
Full integration test for Login Status Checker with API endpoint
"""

import asyncio
import sys
import json
import aiohttp
from pathlib import Path

async def test_api_endpoint():
    """Test the actual API endpoint"""
    print("\n🌐 Testing Login Status Checker API Endpoint...")
    
    base_url = "http://127.0.0.1:8000"
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test 1: Check if server is running
            try:
                async with session.get(f"{base_url}/health") as response:
                    if response.status == 200:
                        print("✅ Backend server is running")
                    else:
                        print(f"⚠️ Backend server responded with status {response.status}")
            except aiohttp.ClientConnectorError:
                print("❌ Backend server is not running")
                print("Please start the backend server first:")
                print("  cd backend && python3 main.py")
                return False
            
            # Test 2: Get profiles to test with
            try:
                async with session.get(f"{base_url}/api/profiles/") as response:
                    if response.status == 200:
                        profiles_data = await response.json()
                        if profiles_data:
                            profile_ids = [p['id'] for p in profiles_data[:2]]  # Test with first 2 profiles
                            print(f"✅ Found {len(profiles_data)} profiles, testing with IDs: {profile_ids}")
                        else:
                            print("⚠️ No profiles found, creating test data...")
                            # Create a test profile
                            test_profile = {
                                "name": "Test Login Check Profile",
                                "type": "facebook",
                                "proxy_config": {
                                    "type": "no_proxy",
                                    "host": None,
                                    "port": None,
                                    "username": None,
                                    "password": None
                                }
                            }
                            
                            async with session.post(f"{base_url}/api/profiles/", json=test_profile) as create_response:
                                if create_response.status == 200:
                                    created_profile = await create_response.json()
                                    profile_ids = [created_profile['id']]
                                    print(f"✅ Created test profile with ID: {profile_ids[0]}")
                                else:
                                    print(f"❌ Failed to create test profile: {create_response.status}")
                                    return False
                    else:
                        print(f"❌ Failed to get profiles: {response.status}")
                        return False
            except Exception as e:
                print(f"❌ Error getting profiles: {e}")
                return False
            
            # Test 3: Test login status check API
            if profile_ids:
                test_request = {
                    "profile_ids": profile_ids,
                    "check_type": "bulk",
                    "platforms": ["facebook", "instagram"]
                }
                
                print(f"\n🧪 Testing login status check with request: {json.dumps(test_request, indent=2)}")
                
                try:
                    async with session.post(f"{base_url}/api/profiles/check-login-status", json=test_request) as response:
                        response_text = await response.text()
                        print(f"📊 Response status: {response.status}")
                        print(f"📊 Response text: {response_text}")
                        
                        if response.status == 200:
                            response_data = json.loads(response_text)
                            print("✅ Login status check API successful!")
                            print(f"📋 Response: {json.dumps(response_data, indent=2)}")
                            
                            # Validate response structure
                            if 'success' in response_data and 'results' in response_data and 'summary' in response_data:
                                print("✅ Response structure is correct")
                                return True
                            else:
                                print("❌ Response structure is incorrect")
                                return False
                        else:
                            print(f"❌ Login status check API failed with status {response.status}")
                            print(f"Response: {response_text}")
                            return False
                            
                except Exception as e:
                    print(f"❌ Error calling login status check API: {e}")
                    return False
            else:
                print("❌ No profile IDs available for testing")
                return False
                
    except Exception as e:
        print(f"❌ API endpoint test failed: {e}")
        return False

async def test_service_directly():
    """Test the service directly without API"""
    print("\n🔧 Testing Login Status Checker Service Directly...")
    
    try:
        from app.services.login_status_checker import LoginStatusChecker
        from app.core.database import AsyncSessionLocal
        from app.models.profile import Profile
        from sqlalchemy import select
        
        # Initialize checker
        checker = LoginStatusChecker()
        print("✅ LoginStatusChecker initialized")
        
        # Test with database
        async with AsyncSessionLocal() as db:
            # Get test profiles
            result = await db.execute(select(Profile).limit(2))
            profiles = result.scalars().all()
            
            if profiles:
                profile_ids = [p.id for p in profiles]
                print(f"✅ Found {len(profiles)} profiles for testing: {profile_ids}")
                
                # Test multiple profiles check (dry run)
                print("🧪 Testing multiple profiles check (preparation only)...")
                platforms = ['facebook', 'instagram']
                
                # This would normally call check_multiple_profiles, but we'll just test the setup
                print(f"   Profile IDs: {profile_ids}")
                print(f"   Platforms: {platforms}")
                print(f"   XPath selectors loaded: {list(checker.xpath_selectors.keys())}")
                print(f"   Platform URLs: {checker.platform_urls}")
                
                print("✅ Service direct test successful (dry run)")
                return True
            else:
                print("⚠️ No profiles found for testing")
                return True
                
    except Exception as e:
        print(f"❌ Service direct test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🚀 Full Login Status Checker Integration Test")
    print("=" * 60)
    
    # Test 1: Service functionality
    service_test = await test_service_directly()
    
    # Test 2: API endpoint (if server is running)
    api_test = await test_api_endpoint()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FULL INTEGRATION TEST SUMMARY")
    print("=" * 60)
    print(f"Service Test: {'✅ PASSED' if service_test else '❌ FAILED'}")
    print(f"API Test: {'✅ PASSED' if api_test else '❌ FAILED'}")
    
    if service_test and api_test:
        print("\n🎉 All integration tests passed!")
        print("\n✅ Login Status Checker is fully functional and ready to use!")
        print("\n📝 You can now:")
        print("   1. Use the frontend UI to check login status")
        print("   2. Call the API endpoint directly")
        print("   3. Check individual or multiple profiles")
        return True
    elif service_test:
        print("\n⚠️ Service works but API test failed (server might not be running)")
        print("\n📝 To test API functionality:")
        print("   1. Start backend server: python3 main.py")
        print("   2. Run this test again")
        return True
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
